"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx":
/*!*********************************************!*\
  !*** ./src/app/ui/crew/allocated-tasks.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleBottomCenterTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleBottomCenterTextIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.3.1/node_modules/@heroicons/react/24/outline/esm/ChatBubbleBottomCenterTextIcon.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst CrewAllocatedTasks = (param)=>{\n    let { taskList } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [filteredTaskList, setFilteredTaskList] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(taskList);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        vessel: null,\n        status: null,\n        keyword: null\n    });\n    // Update filtered list when taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        setFilteredTaskList(taskList);\n    }, [\n        taskList\n    ]);\n    // Apply all filters whenever filters or taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (!taskList) return;\n        let filtered = [\n            ...taskList\n        ];\n        // Apply vessel filter - following the same pattern as maintenance list\n        if (filters.vessel) {\n            // Handle both single and multi-select vessel filtering\n            let vesselIds = [];\n            if (Array.isArray(filters.vessel) && filters.vessel.length > 0) {\n                vesselIds = filters.vessel.map((item)=>String(item.value));\n            } else if (filters.vessel && !Array.isArray(filters.vessel)) {\n                vesselIds = [\n                    String(filters.vessel.value)\n                ];\n            }\n            if (vesselIds.length > 0) {\n                filtered = filtered.filter((task)=>{\n                    var _task_basicComponent;\n                    const taskVesselId = String(task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.id);\n                    return vesselIds.includes(taskVesselId);\n                });\n            }\n        }\n        // Apply status filter\n        if (filters.status) {\n            filtered = filtered.filter((task)=>(task === null || task === void 0 ? void 0 : task.status) === filters.status.value);\n        }\n        // Apply keyword filter\n        if (filters.keyword && filters.keyword.value && filters.keyword.value.trim()) {\n            const keyword = filters.keyword.value.toLowerCase().trim();\n            filtered = filtered.filter((task)=>{\n                // Safely get text content, handling null/undefined and HTML\n                const getName = ()=>((task === null || task === void 0 ? void 0 : task.name) || \"\").toLowerCase();\n                const getDescription = ()=>((task === null || task === void 0 ? void 0 : task.description) || \"\").toLowerCase();\n                const getComments = ()=>{\n                    if (!(task === null || task === void 0 ? void 0 : task.comments)) return \"\";\n                    // Strip HTML tags if present and convert to lowercase\n                    return task.comments.replace(/<[^>]*>/g, \"\").toLowerCase();\n                };\n                const nameMatch = getName().includes(keyword);\n                const descMatch = getDescription().includes(keyword);\n                const commentMatch = getComments().includes(keyword);\n                return nameMatch || descMatch || commentMatch;\n            });\n        }\n        setFilteredTaskList(filtered);\n    }, [\n        taskList,\n        filters\n    ]);\n    // Handle filter changes\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        setFilters((prev)=>({\n                ...prev,\n                [type]: data\n            }));\n    };\n    // Define columns for the DataTable\n    const columns = [\n        {\n            accessorKey: \"title\",\n            header: \"Task\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_isOverDue, _task_isOverDue1, _task_isOverDue2, _task_isOverDue3, _task_isOverDue4, _task_isOverDue5, _task_isOverDue6, _task_isOverDue7, _task_isOverDue8, _task_isOverDue9, _task_isOverDue10, _task_isOverDue11, _task_isOverDue12, _task_isOverDue13, _task_isOverDue14;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-foreground flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(task.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: \"focus:outline-none\",\n                                            children: task.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 \".concat((task === null || task === void 0 ? void 0 : (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\" ? \"inline-block rounded px-3 py-1 alert\" : \"inline-block\"),\n                                            children: [\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status) && [\n                                                    \"High\",\n                                                    \"Medium\",\n                                                    \"Low\"\n                                                ].includes(task.isOverDue.status) && (task === null || task === void 0 ? void 0 : (_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue3 = task.isOverDue) === null || _task_isOverDue3 === void 0 ? void 0 : _task_isOverDue3.status) === \"Completed\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue4 = task.isOverDue) === null || _task_isOverDue4 === void 0 ? void 0 : _task_isOverDue4.days) === \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue5 = task.isOverDue) === null || _task_isOverDue5 === void 0 ? void 0 : _task_isOverDue5.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue6 = task.isOverDue) === null || _task_isOverDue6 === void 0 ? void 0 : _task_isOverDue6.status) === \"Upcoming\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue7 = task.isOverDue) === null || _task_isOverDue7 === void 0 ? void 0 : _task_isOverDue7.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue8 = task.isOverDue) === null || _task_isOverDue8 === void 0 ? void 0 : _task_isOverDue8.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue9 = task.isOverDue) === null || _task_isOverDue9 === void 0 ? void 0 : _task_isOverDue9.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue10 = task.isOverDue) === null || _task_isOverDue10 === void 0 ? void 0 : _task_isOverDue10.status),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue11 = task.isOverDue) === null || _task_isOverDue11 === void 0 ? void 0 : _task_isOverDue11.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue12 = task.isOverDue) === null || _task_isOverDue12 === void 0 ? void 0 : _task_isOverDue12.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue13 = task.isOverDue) === null || _task_isOverDue13 === void 0 ? void 0 : _task_isOverDue13.days) !== \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue14 = task.isOverDue) === null || _task_isOverDue14 === void 0 ? void 0 : _task_isOverDue14.days)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-14 flex items-center pl-1\",\n                                    children: task.Comments !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"outline-none px-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleBottomCenterTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverContent, {\n                                                className: \"w-64\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"leading-loose\",\n                                                    children: task.comments\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 25\n                        }, undefined),\n                        task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1 text-sm text-muted-foreground\",\n                            children: task.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: \"Vessel\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_basicComponent;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: (task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.title) || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"inventoryItem\",\n            header: \"Inventory Item\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_inventory, _task_maintenanceSchedule_inventory, _task_maintenanceSchedule;\n                const task = row.original;\n                // Check for inventory from multiple possible sources\n                const inventoryItem = (task === null || task === void 0 ? void 0 : (_task_inventory = task.inventory) === null || _task_inventory === void 0 ? void 0 : _task_inventory.item) || (task === null || task === void 0 ? void 0 : (_task_maintenanceSchedule = task.maintenanceSchedule) === null || _task_maintenanceSchedule === void 0 ? void 0 : (_task_maintenanceSchedule_inventory = _task_maintenanceSchedule.inventory) === null || _task_maintenanceSchedule_inventory === void 0 ? void 0 : _task_maintenanceSchedule_inventory.item) || null;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: inventoryItem || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-right\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground mb-1\",\n                            children: [\n                                \"Due: \",\n                                (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(task.expires)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block rounded px-3 py-1 \".concat(task.status == \"Completed\" ? \"success\" : \"alert\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: task.status\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: !taskList ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.List, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 235,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n            columns: columns,\n            data: filteredTaskList || [],\n            showToolbar: true,\n            pageSize: 50,\n            showPageSizeSelector: false,\n            onChange: handleFilterOnChange\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 237,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s(CrewAllocatedTasks, \"ma6jPe/tw3b936HXYaWPMLLamsk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams\n    ];\n});\n_c = CrewAllocatedTasks;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewAllocatedTasks);\nvar _c;\n$RefreshReg$(_c, \"CrewAllocatedTasks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx\n"));

/***/ })

});