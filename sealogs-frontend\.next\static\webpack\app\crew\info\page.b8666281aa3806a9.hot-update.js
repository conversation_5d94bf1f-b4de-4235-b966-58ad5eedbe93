"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx":
/*!*********************************************!*\
  !*** ./src/app/ui/crew/allocated-tasks.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst CrewAllocatedTasks = (param)=>{\n    let { taskList } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [filteredTaskList, setFilteredTaskList] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(taskList);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        vessel: null,\n        status: null,\n        keyword: null\n    });\n    // Update filtered list when taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        setFilteredTaskList(taskList);\n    }, [\n        taskList\n    ]);\n    // Apply all filters whenever filters or taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (!taskList) return;\n        let filtered = [\n            ...taskList\n        ];\n        // Apply vessel filter - following the same pattern as maintenance list\n        if (filters.vessel) {\n            // Handle both single and multi-select vessel filtering\n            let vesselIds = [];\n            if (Array.isArray(filters.vessel) && filters.vessel.length > 0) {\n                vesselIds = filters.vessel.map((item)=>String(item.value));\n            } else if (filters.vessel && !Array.isArray(filters.vessel)) {\n                vesselIds = [\n                    String(filters.vessel.value)\n                ];\n            }\n            if (vesselIds.length > 0) {\n                filtered = filtered.filter((task)=>{\n                    var _task_basicComponent;\n                    const taskVesselId = String(task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.id);\n                    return vesselIds.includes(taskVesselId);\n                });\n            }\n        }\n        // Apply status filter\n        if (filters.status) {\n            filtered = filtered.filter((task)=>(task === null || task === void 0 ? void 0 : task.status) === filters.status.value);\n        }\n        // Apply keyword filter\n        if (filters.keyword && filters.keyword.value && filters.keyword.value.trim()) {\n            const keyword = filters.keyword.value.toLowerCase().trim();\n            filtered = filtered.filter((task)=>{\n                // Safely get text content, handling null/undefined and HTML\n                const getName = ()=>((task === null || task === void 0 ? void 0 : task.name) || \"\").toLowerCase();\n                const getDescription = ()=>((task === null || task === void 0 ? void 0 : task.description) || \"\").toLowerCase();\n                const getComments = ()=>{\n                    if (!(task === null || task === void 0 ? void 0 : task.comments)) return \"\";\n                    // Strip HTML tags if present and convert to lowercase\n                    return task.comments.replace(/<[^>]*>/g, \"\").toLowerCase();\n                };\n                const nameMatch = getName().includes(keyword);\n                const descMatch = getDescription().includes(keyword);\n                const commentMatch = getComments().includes(keyword);\n                return nameMatch || descMatch || commentMatch;\n            });\n        }\n        setFilteredTaskList(filtered);\n    }, [\n        taskList,\n        filters\n    ]);\n    // Handle filter changes\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        setFilters((prev)=>({\n                ...prev,\n                [type]: data\n            }));\n    };\n    // Define columns for the DataTable\n    const columns = [\n        {\n            accessorKey: \"title\",\n            header: \"Task\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_isOverDue, _task_isOverDue1, _task_isOverDue2, _task_isOverDue3, _task_isOverDue4, _task_isOverDue5, _task_isOverDue6, _task_isOverDue7, _task_isOverDue8, _task_isOverDue9, _task_isOverDue10, _task_isOverDue11, _task_isOverDue12, _task_isOverDue13, _task_isOverDue14;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-foreground flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(task.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: \"focus:outline-none\",\n                                            children: task.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 \".concat((task === null || task === void 0 ? void 0 : (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\" ? \"inline-block rounded px-3 py-1 alert\" : \"inline-block\"),\n                                            children: [\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status) && [\n                                                    \"High\",\n                                                    \"Medium\",\n                                                    \"Low\"\n                                                ].includes(task.isOverDue.status) && (task === null || task === void 0 ? void 0 : (_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue3 = task.isOverDue) === null || _task_isOverDue3 === void 0 ? void 0 : _task_isOverDue3.status) === \"Completed\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue4 = task.isOverDue) === null || _task_isOverDue4 === void 0 ? void 0 : _task_isOverDue4.days) === \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue5 = task.isOverDue) === null || _task_isOverDue5 === void 0 ? void 0 : _task_isOverDue5.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue6 = task.isOverDue) === null || _task_isOverDue6 === void 0 ? void 0 : _task_isOverDue6.status) === \"Upcoming\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue7 = task.isOverDue) === null || _task_isOverDue7 === void 0 ? void 0 : _task_isOverDue7.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue8 = task.isOverDue) === null || _task_isOverDue8 === void 0 ? void 0 : _task_isOverDue8.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue9 = task.isOverDue) === null || _task_isOverDue9 === void 0 ? void 0 : _task_isOverDue9.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue10 = task.isOverDue) === null || _task_isOverDue10 === void 0 ? void 0 : _task_isOverDue10.status),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue11 = task.isOverDue) === null || _task_isOverDue11 === void 0 ? void 0 : _task_isOverDue11.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue12 = task.isOverDue) === null || _task_isOverDue12 === void 0 ? void 0 : _task_isOverDue12.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue13 = task.isOverDue) === null || _task_isOverDue13 === void 0 ? void 0 : _task_isOverDue13.days) !== \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue14 = task.isOverDue) === null || _task_isOverDue14 === void 0 ? void 0 : _task_isOverDue14.days)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-14 flex items-center pl-1\",\n                                    children: task.Comments !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    iconOnly: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverContent, {\n                                                className: \"w-64\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"leading-loose\",\n                                                    children: task.comments\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 25\n                        }, undefined),\n                        task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1 text-sm text-muted-foreground\",\n                            children: task.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: \"Vessel\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_basicComponent;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: (task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.title) || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"inventoryItem\",\n            header: \"Inventory Item\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_inventory, _task_maintenanceSchedule_inventory, _task_maintenanceSchedule;\n                const task = row.original;\n                // Check for inventory from multiple possible sources\n                const inventoryItem = (task === null || task === void 0 ? void 0 : (_task_inventory = task.inventory) === null || _task_inventory === void 0 ? void 0 : _task_inventory.item) || (task === null || task === void 0 ? void 0 : (_task_maintenanceSchedule = task.maintenanceSchedule) === null || _task_maintenanceSchedule === void 0 ? void 0 : (_task_maintenanceSchedule_inventory = _task_maintenanceSchedule.inventory) === null || _task_maintenanceSchedule_inventory === void 0 ? void 0 : _task_maintenanceSchedule_inventory.item) || null;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: inventoryItem || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_isOverDue;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-right\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground mb-1\",\n                            children: [\n                                \"Due: \",\n                                (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(task.expires)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(task.status !== \"Completed\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\" ? \"inline-block rounded px-3 py-1 alert\" : \"inline-block\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: task.status\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: !taskList ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.List, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 237,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n            columns: columns,\n            data: filteredTaskList || [],\n            showToolbar: true,\n            pageSize: 50,\n            showPageSizeSelector: false,\n            onChange: handleFilterOnChange\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 239,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s(CrewAllocatedTasks, \"ma6jPe/tw3b936HXYaWPMLLamsk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams\n    ];\n});\n_c = CrewAllocatedTasks;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewAllocatedTasks);\nvar _c;\n$RefreshReg$(_c, \"CrewAllocatedTasks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx\n"));

/***/ })

});